body {
    font-family: 'Inter', Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

.user-form-bg {
    min-height: 100vh;
    width: 100vw;
    background: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}
.user-card {
    background: #fff;
    box-shadow: 0 2px 16px 0 rgba(60,72,88,0.10);
    border-radius: 16px;
    padding: 40px 32px 32px 32px;
    max-width: 420px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 22px;
    margin: 40px 0;
    border: 1px solid #ececec;
    animation: fadein 0.7s;
}
.user-card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #222;
    margin-bottom: 10px;
    text-align: center;
    letter-spacing: -0.5px;
}
.user-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.user-input {
    font-size: 1rem;
    padding: 13px 14px;
    border: 1.5px solid #e4e7ec;
    border-radius: 8px;
    background: #fafbfc;
    color: #222;
    outline: none;
    transition: border 0.18s;
    box-sizing: border-box;
    font-family: inherit;
    resize: none;
}
.user-input:focus {
    border: 1.5px solid #5cb85c;
    background: #fff;
}
.user-submit {
    background: #27ae60;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.08rem;
    font-weight: 600;
    padding: 13px 0;
    cursor: pointer;
    transition: background 0.18s, box-shadow 0.18s;
    box-shadow: 0 1px 6px 0 rgba(39,174,96,0.08);
    margin-top: 10px;
}
.user-submit:hover, .user-submit:focus {
    background: #219150;
    box-shadow: 0 2px 12px 0 rgba(39,174,96,0.12);
}
@media (max-width: 600px) {
    .user-card {
        padding: 24px 7vw 22px 7vw;
        max-width: 98vw;
    }
    .user-form {
        gap: 12px;
    }
    .user-card-title {
        font-size: 1.18rem;
    }
}

h1, h2 {
    color: #333;
}

section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}



#summarySection {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
}
