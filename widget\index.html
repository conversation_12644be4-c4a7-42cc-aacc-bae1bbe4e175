<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Widget Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 32px;
            font-size: 18px;
        }
        
        .demo-section {
            margin-bottom: 32px;
            padding: 24px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .demo-section h2 {
            color: #374151;
            margin-bottom: 16px;
            font-size: 20px;
        }
        
        .demo-section p {
            color: #6b7280;
            margin-bottom: 16px;
        }
        
        .demo-controls {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .demo-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .demo-btn.primary {
            background: #00C2A8;
            color: white;
            border-color: #00C2A8;
        }
        
        .demo-btn.primary:hover {
            background: #00a58e;
            border-color: #00a58e;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin-top: 16px;
        }
        
        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
            color: #1f2937;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Feedback Widget Demo</h1>
        <p class="subtitle">Test the embeddable feedback widget</p>
        
        <div class="demo-section">
            <h2>Widget Controls</h2>
            <p>Use these buttons to test the widget functionality:</p>
            <div class="demo-controls">
                <button class="demo-btn primary" onclick="FeedbackWidget.open()">Open Widget</button>
                <button class="demo-btn" onclick="FeedbackWidget.close()">Close Widget</button>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Integration Code</h2>
            <p>Copy this code to embed the widget on any website:</p>
            
            <div class="code-block">
&lt;!-- Add this before closing &lt;/body&gt; tag --&gt;<br>
&lt;script src="<span class="highlight">https://your-widget-domain.vercel.app/widget.js</span>"&gt;&lt;/script&gt;<br>
&lt;script&gt;<br>
&nbsp;&nbsp;FeedbackWidget.init({<br>
&nbsp;&nbsp;&nbsp;&nbsp;position: 'bottom-right',<br>
&nbsp;&nbsp;&nbsp;&nbsp;primaryColor: '#00C2A8',<br>
&nbsp;&nbsp;&nbsp;&nbsp;title: 'Feedback'<br>
&nbsp;&nbsp;});<br>
&lt;/script&gt;
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Features</h2>
            <ul style="color: #374151; line-height: 1.8;">
                <li>✅ Self-contained JavaScript widget</li>
                <li>✅ Floating feedback button</li>
                <li>✅ Responsive design</li>
                <li>✅ Dark theme support</li>
                <li>✅ Keyboard accessibility</li>
                <li>✅ Form validation</li>
                <li>✅ API integration</li>
            </ul>
        </div>
    </div>

    <!-- Load the feedback widget -->
    <script src="widget.js"></script>
    <script>
        // Initialize the widget with default settings
        FeedbackWidget.init({
            position: 'bottom-right',
            primaryColor: '#00C2A8',
            title: 'Feedback'
        });
    </script>
</body>
</html>
