@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');
body {
    font-family: 'Inter', Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f7f8fa;
    color: #222;
}

.container {
    max-width: 1200px;
    width: 98%;
    margin: 32px auto 32px auto;
    padding: 0 0 32px 0;
    background: transparent;
    box-sizing: border-box;
}

@media (max-width: 700px) {
    .container {
        max-width: 100vw;
        width: 100%;
        padding: 6px;
    }
    .dashboard-section {
        padding: 8px 0 0 0;
    }
    .summary-block, .result-block {
        font-size: 0.97rem;
        padding: 8px;
    }
    #issuesChart, #sentimentChart {
        padding: 3px;
    }
    .query-input-row {
        flex-direction: column;
        gap: 6px;
    }
    .suggestion-buttons {
        gap: 6px;
    }
    .top-row {
        flex-direction: column !important;
    }
    #summarySection, #querySection {
        width: 100% !important;
        min-width: 0 !important;
        margin-right: 0 !important;
    }
}

.dashboard-header {
    text-align: center;
    margin: 0 auto 38px auto;
    max-width: 700px;
    padding: 40px 0 0 0;
}
.dashboard-title {
    font-size: 2.3rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    color: #23272f;
    margin-bottom: 0.2em;
}
.dashboard-subtitle {
    color: #6b7280;
    font-size: 1.12rem;
    font-weight: 400;
    margin-bottom: 0;
}


.dashboard-card {
    background: #fff;
    box-shadow: 0 2px 16px 0 rgba(60,72,88,0.08);
    border-radius: 14px;
    padding: 32px 28px 28px 28px;
    border: 1px solid #ececec;
    min-width: 0;
    width: 100%;
    min-height: 340px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    transition: box-shadow 0.18s;
    position: relative;
    animation: fadein 0.7s;
    margin: 0;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
}
@media (max-width: 900px) {
    .dashboard-card {
        padding: 22px 10px 22px 10px;
        min-height: 260px;
    }
}
.card-title.center {
    text-align: center;
}

.card-title {
    font-size: 1.18rem;
    font-weight: 600;
    color: #23272f;
    margin-bottom: 10px;
    letter-spacing: 0.01em;
}
@media (max-width: 700px) {
    .dashboard-card {
        padding: 18px 8px 18px 8px;
        margin-bottom: 18px;
    }
}


.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    column-gap: 40px;
    row-gap: 40px;
    margin: 0 auto 40px auto;
    max-width: 1100px;
    box-sizing: border-box;
}
@media (max-width: 900px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: none;
        column-gap: 0;
        row-gap: 24px;
        max-width: 98vw;
    }
}


.charts-row {
    display: flex;
    flex-direction: row;
    gap: 32px;
    justify-content: center;
    margin: 0 auto 32px auto;
    max-width: 900px;
}
@media (max-width: 900px) {
    .charts-row {
        flex-direction: column;
        gap: 18px;
        max-width: 100vw;
    }
}


@media (max-width: 900px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}
#summarySection, #querySection {
    min-width: 0;
    margin-right: 0;
}
.summary-block, .result-block {
    background: #f7f8fa;
    padding: 20px 18px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    min-height: 110px;
    max-height: 220px;
    font-size: 1.08rem;
    color: #23272f;
    margin-bottom: 10px;
    overflow-y: auto;
    box-sizing: border-box;
    width: 100%;
    resize: none;
    font-family: inherit;
    line-height: 1.6;
    box-shadow: 0 1px 4px 0 rgba(60,72,88,0.03);
    transition: all 0.2s ease;
}

/* Summary text states */
.summary-text {
    transition: all 0.2s ease;
}

.summary-text.loading {
    color: #4b5563;
    font-style: italic;
    position: relative;
}

.summary-text.loading::after {
    content: '...';
    display: inline-block;
    width: 1em;
    overflow: hidden;
    vertical-align: bottom;
    animation: ellipsis 1.5s infinite steps(4, end);
}

@keyframes ellipsis {
    0% { width: 0.2em; }
    50% { width: 1em; }
    100% { width: 0.2em; }
}

.summary-text.error {
    color: #dc2626;
    background-color: rgba(220, 38, 38, 0.05);
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #dc2626;
}
.summary-block:focus, .result-block:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}


h2 {
    color: #333;
    margin-bottom: 12px;
    font-size: 1.25rem;
    letter-spacing: 0.02em;
}


.summary-box-wrapper {
    margin-bottom: 10px;
}

.summary-box {
    width: 100%;
    min-height: 90px;
    max-height: 180px;
    resize: vertical;
    font-size: 1rem;
    padding: 14px;
    border-radius: 6px;
    border: 1px solid #ddd;
    background: #fff;
    color: #222;
    box-sizing: border-box;
}

.summary-controls {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 0;
}

.btn {
    font-family: inherit;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 5px;
    padding: 9px 20px;
    border: none;
    cursor: pointer;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
    outline: none;
    box-shadow: 0 1px 2px 0 rgba(60,72,88,0.03);
}
.btn-primary {
    background: #2563eb;
    color: #fff;
    border: 1px solid #2563eb;
}
.btn-primary:hover, .btn-primary:focus {
    background: #1743a2;
    color: #fff;
    box-shadow: 0 2px 8px 0 rgba(37,99,235,0.10);
}
.btn-secondary {
    background: #f7f8fa;
    color: #23272f;
    border: 1px solid #cbd5e1;
}

/* Sample Data Section */
.sample-data-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-control {
    padding: 10px 14px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.status-message {
    margin: 8px 0 0 0;
    min-height: 24px;
    font-size: 0.95rem;
    line-height: 1.4;
}

.status-message.success {
    color: #059669;
}

.status-message.error {
    color: #dc2626;
}

#generateDataBtn {
    align-self: flex-start;
    margin-top: 8px;
}

#generateDataBtn:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sample-data-form {
        gap: 14px;
    }
    
    .form-control {
        padding: 8px 12px;
    }
}
.btn-secondary:hover, .btn-secondary:focus {
    background: #e5e7eb;
    color: #23272f;
}


.query-tool {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.query-input-row {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
}
#queryInput {
    flex: 1;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
}
#submitQuery {
    padding: 10px 18px;
    border-radius: 4px;
    background: #2563eb;
    color: #fff;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}
#submitQuery:hover {
    background: #1743a2;
}
.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 8px;
}
.btn-pill {
    padding: 7px 18px;
    border-radius: 999px;
    background: #f3f6fa;
    border: 1.5px solid #e5e7eb;
    color: #2563eb;
    font-size: 0.97rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.18s, color 0.18s, border 0.18s;
}
.btn-pill:hover, .btn-pill:focus {
    background: #e7edfa;
    color: #1743a2;
    border-color: #2563eb;
}

.result-box {
    width: 100%;
    min-height: 70px;
    max-height: 150px;
    resize: vertical;
    font-size: 1rem;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #ddd;
    background: #fff;
    color: #222;
    box-sizing: border-box;
}


#issuesSection, #sentimentSection {
    margin-bottom: 32px;
}

#issuesChart, #sentimentChart {
    display: block;
    width: 100%;
    max-width: 100%;
    min-width: 0;
    background: #fff;
    padding: 0;
    margin: 0 auto 8px auto;
    border-radius: 8px;
    box-shadow: none;
    border: none;
    min-height: 160px;
    box-sizing: border-box;
    overflow: auto;
}

.error-message {
    color: #dc3545;
    text-align: center;
    margin-top: 10px;
    font-size: 0.99rem;
}

.fade-in {
    animation: fadein 0.7s;
}
@keyframes fadein {
    from { opacity: 0; transform: translateY(18px); }
    to   { opacity: 1; transform: none; }
}


#suggestedQuestions {
    margin-top: 20px;
}

.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.suggestion {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    transition: background-color 0.3s, color 0.3s;
    cursor: pointer;
}

.suggestion:hover {
    background-color: #e0e0e0;
}

.suggestion:disabled {
    background-color: #f0f0f0;
    color: #999;
    cursor: not-allowed;
}
