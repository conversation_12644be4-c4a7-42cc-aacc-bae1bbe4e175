{"version": 2, "builds": [{"src": "**/*", "use": "@vercel/static"}], "routes": [{"src": "/widget.js", "dest": "/widget.js", "headers": {"Content-Type": "application/javascript", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, OPTIONS", "Access-Control-Allow-Headers": "Content-Type"}}, {"src": "/widget.css", "dest": "/widget.css", "headers": {"Content-Type": "text/css", "Access-Control-Allow-Origin": "*"}}, {"src": "/demo.html", "dest": "/demo.html", "headers": {"Content-Type": "text/html"}}, {"src": "/", "dest": "/demo.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}