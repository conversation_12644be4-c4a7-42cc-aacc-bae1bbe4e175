# Vercel Deployment Guide

## Current Issues Fixed

### 1. API URL Configuration
- ✅ Changed from `http://127.0.0.1:5000/submit_comment` to `https://user-comments-backend.onrender.com/submit_comment`
- ✅ Widget now points to your deployed backend

### 2. Vercel Configuration
- ✅ Added `vercel.json` with proper static file serving
- ✅ Added CORS headers for cross-origin requests
- ✅ Added proper routing for widget files

### 3. Required Files
- ✅ `package.json` - Project metadata
- ✅ `index.html` - Landing page (replaces demo.html)
- ✅ `.vercelignore` - Files to ignore during deployment

## Deployment Steps

1. **Commit and Push Changes**
   ```bash
   git add .
   git commit -m "Fix Vercel deployment configuration"
   git push origin main
   ```

2. **Vercel Auto-Deploy**
   - Vercel should automatically detect the changes
   - New deployment will be triggered
   - Check deployment status in Vercel dashboard

3. **Test the Widget**
   - Visit your Vercel URL (e.g., `https://your-widget.vercel.app`)
   - Test the widget functionality
   - Verify API calls work to your backend

## Widget Usage

Once deployed, users can embed your widget like this:

```html
<script src="https://your-widget.vercel.app/widget.js"></script>
<script>
  FeedbackWidget.init({
    position: 'bottom-right',
    primaryColor: '#00C2A8',
    title: 'Feedback'
  });
</script>
```

## Files Structure

```
widget/
├── widget.js          # Main widget code
├── widget.css         # Widget styles (if separate)
├── index.html         # Demo page
├── demo.html          # Original demo
├── README.md          # Documentation
├── package.json       # Project config
├── vercel.json        # Vercel config
├── .vercelignore      # Ignore file
└── DEPLOYMENT.md      # This guide
```

## Troubleshooting

### 404 Errors
- Ensure `vercel.json` is properly configured
- Check that all files are committed and pushed
- Verify Vercel deployment logs

### CORS Issues
- Backend must allow requests from your widget domain
- Check CORS configuration in your Flask app

### API Connection Issues
- Verify backend URL is correct and accessible
- Check network tab in browser dev tools
- Ensure backend is running and healthy
