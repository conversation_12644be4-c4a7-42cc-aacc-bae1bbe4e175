<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="stylesheet" href="admin.css">
</head>
<body>
    <div class="container">
        <header class="dashboard-header fade-in" style="display: flex; align-items: center; justify-content: space-between; gap: 18px;">
            <div>
                <h1 class="dashboard-title">Admin Dashboard</h1>
                <p class="dashboard-subtitle">Monitor user sentiment, query feedback, and view key issues at a glance.</p>
            </div>
            <a href="index.html" class="btn btn-primary nav-btn">Go to User Page</a>
        </header>

        <main>
            <div class="dashboard-grid fade-in">
                <section id="sampleDataSection" class="dashboard-card">
                    <h2 class="card-title center">Generate Sample Data</h2>
                    <div class="sample-data-form">
                        <div class="form-group">
                            <label for="promptInput">Prompt</label>
                            <input type="text" id="promptInput" class="form-control" placeholder="e.g., positive comments about Best Buy">
                        </div>
                        <div class="form-group">
                            <label for="countInput">How many? (1-20)</label>
                            <input type="number" id="countInput" class="form-control" min="1" max="20" value="5">
                        </div>
                        <button id="generateDataBtn" class="btn btn-primary">Generate Comments</button>
                        <p id="generateStatus" class="status-message"></p>
                    </div>
                </section>

                <section id="summarySection" class="dashboard-card">
                    <h2 class="card-title center">Summary</h2>
                    <div class="summary-box-wrapper">
                        <div id="summaryText" class="summary-block">No summary available yet.</div>
                    </div>
                    <div class="summary-controls">
                        <button id="refreshSummary" class="btn btn-secondary">Refresh Summary</button>
                    </div>
                </section>

                <section id="querySection" class="dashboard-card">
                    <h2 class="card-title center">Query Comments</h2>
                    <div class="query-tool">
                        <div class="query-input-row">
                            <input type="text" id="queryInput" placeholder="Ask a question about the comments" />
                            <button id="submitQuery" class="btn btn-primary">Ask</button>
                        </div>
                        <div id="suggestedQuestions">
                            <div class="suggestion-buttons">
                                <button class="suggestion btn-pill" data-query="What are the top complaints?">Top Complaints</button>
                                <button class="suggestion btn-pill" data-query="What do users love about the companies?">Users Love...</button>
                                <button class="suggestion btn-pill" data-query="Summarize key changes in comments over the past 30 days">Changes (Past 30 Days)</button>
                                <button class="suggestion btn-pill" data-query="Identify emerging trends in user feedback">Emerging Trends</button>
                            </div>
                        </div>
                        <div id="queryResult">
                            <label for="queryResultText">Result</label>
                            <div id="queryResultText" class="result-block">Query results will appear here.</div>
                        </div>
                    </div>
                </section>

                <section id="issuesSection" class="dashboard-card">
                    <h2 class="card-title center">Most Common Issues</h2>
                    <canvas id="issuesChart" width="320" height="160"></canvas>
                    <p id="issuesChartError" class="error-message"></p>
                </section>

                <section id="sentimentSection" class="dashboard-card">
                    <h2 class="card-title center">User Sentiment Over Time</h2>
                    <canvas id="sentimentChart" width="320" height="160"></canvas>
                    <p id="sentimentChartError" class="error-message"></p>
                </section>
            </div>
        </main>
    </div>

    <script src="admin.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>
